from django.db import models
from django.contrib.auth.models import User


class UserProfile(models.Model):
    ROLE_CHOICES=[
        ('student','Student'),
        ('teacher','Teacher'),
    ]
    user =models.OneToOneField(User,on_delete=models.CASCADE)
    full_name=models.CharField(max_length=100)
    role=models.CharField(max_length=10,choices=ROLE_CHOICES)


class Teacher(models.Model):
    profile=models.OneToOneField(UserProfile,on_delete= models.CASCADE)
    # earning=models.IntegerField(default=0)


class Course(models.Model):
    title=models.CharField(max_length=100)
    description=models.TextField()
    teacher=models.ForeignKey(Teacher,on_delete=models.CASCADE)
    enrolledStudents=models.ForeignKey(UserProfile,on_delete=models.CASCADE,null=True)
