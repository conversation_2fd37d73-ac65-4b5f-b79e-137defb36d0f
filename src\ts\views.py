from django.shortcuts import render,redirect
from .models import Course
from django.contrib.auth.models import User
from .models import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,Teacher
from django.contrib.auth import login,authenticate

# Create your views here.
def teacher(request):
    if request.method=="POST":
        id=request.user.userprofile.id
        name=request.POST.get('course_name')
        description=request.POST.get('course_description')
        Course.objects.create(title=name,description=description,teacher_id=id)

    return render(request,"ts/teacher.html")

def student(request):
    

    return render(request,"ts/student.html")

def signup(request):
    print("post call?")
    if request.method=="POST":
        full_name=request.POST.get("username")
        password=request.POST.get("password")
        role = request.POST.get("userType")

        user = User.objects.create_user(username=full_name, password=password)
        print("user ma aayo")
        user_profile = UserProfile.objects.create(
                    user=user,
                    full_name=full_name,
                    role=role
                )
        if role == 'teacher':
                    print("teacher ma ayo")
                    Teacher.objects.create(profile=user_profile)
                    return redirect('login')


    return render(request,"ts/signup.html")

def login_View(request):
    if request.method == "POST":
        print("login airaxa")
        name=request.POST.get("username")
        password=request.POST.get("password")

        if name and password:
            user=authenticate(request,username=name,password=password)

            if user is not None:
                login(request,user)
                print("login vayo")
                return redirect('student')
            else:
                 print("fail")

    return render(request, "ts/login.html")
